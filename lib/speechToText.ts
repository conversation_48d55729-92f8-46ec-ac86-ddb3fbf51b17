// 音声から文字起こしができた場合に呼び出される関数
export type TranscriptCallback = (
  transcription: string,
  isFinal: boolean,
) => void;
// 文字起こしが停止したら呼び出される関数
export type RecognitionEndCallback = () => void;

export interface ISpeechToTextParams {
  transcriptCallback: TranscriptCallback;
  silenceThreshold: number;
}

/**
 * ブラウザーのSpeechToText
 *
 * stateなどの状態はなく、イベントから状態を更新する必要がある。
 *
 * 一連のイベント順：
 * 1. recognition.start() （ユーザーがマイクボタンを押した）
 * 2. onstart (recognitionサービス開始イベント)
 * 3. onaudiostart (audio capture開始。これはあくまでもマイクが有効になったというイベント)
 * 4. onsoundstart (何かしらの音を検知したというイベント。雑音含む)
 * 5. onspeechstart (雑音とかではなく、人間からの「発話」が検知されたというイベント)
 * 6. onresult (文字起こしができたというイベント)
 * 7. onspeechend (「発話の停止」が検知されたというイベント)
 * 8. onsoundend (雑音を含む音が検知できなくなったというイベント)
 * 9. onaudioend (マイクがOFFになったというイベント)
 * 10. onend (recognitionサービスが終了したというイベント)
 *
 * abort()の入り点のイベント順：
 * 1. recognition.abort()（ユーザーがマイクボタンを押した）
 * 2. onend (recognitionサービスが終了したというイベント)
 *
 * 参考までにabort()とstop()に違い
 * stop()だと以下のイベントが発生する：
 * 1. recognition.stop()（例えばユーザーがマイクボタンを押した）
 * 2. onspeechend
 * 3. onresult （stop後にバッファーされた音声の文字起こしがまだ続いている）
 * 3. onsoundend
 * 4. onaudioend
 * 5. onend
 * 今回のSpeechToTextでは停止ボタンを押したらすぐに停止したいため、stop()は使用しない。
 */
export class SpeechToText {
  private recognition: SpeechRecognition | null = null;
  private isListening: boolean = false;
  private silenceTimer: NodeJS.Timeout | null = null;
  private transcriptCallback: TranscriptCallback;
  private silenceThreshold: number; // SilenceCallbackを呼び出すまでの経過時間(ms)
  private lastTranscript: string = "";
  private stoppedByUser: boolean = false; // ユーザーが停止したフラグ
  private static instance: SpeechToText;

  constructor(params: ISpeechToTextParams) {
    console.debug("SpeechToText constructor called");
    this.transcriptCallback = params.transcriptCallback;
    this.silenceThreshold = params.silenceThreshold;
    this.initializeSpeechRecognition();
  }

  static getInstance(params: ISpeechToTextParams): SpeechToText {
    if (!SpeechToText.instance) {
      SpeechToText.instance = new SpeechToText(params);
    }
    return SpeechToText.instance;
  }

  static isSpeechRecognitionAvailable(): boolean {
    if (
      !window ||
      (!("SpeechRecognition" in window) &&
        !("webkitSpeechRecognition" in window))
    ) {
      return false;
    }
    return true;
  }

  private initializeSpeechRecognition(): void {
    if (!SpeechToText.isSpeechRecognitionAvailable()) {
      throw new Error("Speech recognition is not supported in this browser.");
    }

    const SpeechRecognition =
      window?.SpeechRecognition || window?.webkitSpeechRecognition;
    this.recognition = new SpeechRecognition();
    this.recognition.continuous = true;
    this.recognition.interimResults = true;
    this.recognition.lang = "ja-JP";

    this.recognition.onresult = this.handleRecognitionResult.bind(this);
    this.recognition.onerror = this.handleRecognitionError.bind(this);
    this.recognition.onend = this.handleRecognitionEnd.bind(this);
  }

  private handleRecognitionResult(event: SpeechRecognitionEvent): void {
    const lastResult = event.results[event.results.length - 1];
    if (lastResult.isFinal) {
      // 文字起こしのisFinalイベントの前に送信しているので、
      // isFinalがきたら送信済みだという意味
      console.debug(
        "SpeechToText handleRecognitionResult isFinal, doing nothing. lastTranscript = ",
        this.lastTranscript,
      );
      return;
    }
    this.resetSilenceTimer();
    console.debug("SpeechToText event.results =", event.results);

    let interimTranscript = "";
    Array.from(event.results).forEach((result) => {
      console.debug("SpeechToText result =", result);
      interimTranscript += result[0].transcript;
      this.transcriptCallback(interimTranscript, false);
    });
    this.lastTranscript = interimTranscript;
  }

  private handleRecognitionError(event: SpeechRecognitionErrorEvent): void {
    console.debug("SpeechToText handleRecognitionError error:", event.error);
  }

  private handleRecognitionEnd(): void {
    console.debug("SpeechToText handleRecogitionEnd called");
    this.clearSilenceTimer();
    this.isListening = false;
    this.startListening(); // 自動再開
  }

  private resetSilenceTimer(): void {
    this.clearSilenceTimer();
    this.silenceTimer = setTimeout(() => {
      console.debug("SpeechToText silence timeout. Aborting listening");
      this.abortListening();
      this.transcriptCallback(this.lastTranscript, true);
      this.lastTranscript = "";
    }, this.silenceThreshold);
  }

  private clearSilenceTimer(): void {
    if (!this.silenceTimer) {
      return;
    }
    clearTimeout(this.silenceTimer);
    this.silenceTimer = null;
  }

  public startListening(startedByUser: boolean = false): void {
    console.debug("SpeechToText startListening called");
    if (startedByUser) {
      this.stoppedByUser = false;
    }
    if (this.stoppedByUser) {
      // ユーザーが停止しているなら、
      // ユーザーが再開するまで自動再開しない
      return;
    }
    try {
      this.recognition?.start();
    } catch (e: unknown) {
      console.debug("SpeechToText startListening exception:", e);
    }
    this.isListening = true;
    this.stoppedByUser = false;
  }

  // マイクを停止して文字起こしも同時に停止する
  // 結果、この後に文字起こしイベントは発生しない
  public abortListening(stoppedByUser: boolean = false): void {
    console.debug("SpeechToText abortListening called");
    try {
      this.recognition?.abort();
    } catch (e: unknown) {
      console.debug("SpeechToText abortListening exception:", e);
    }
    if (stoppedByUser) {
      this.stoppedByUser = true;
      this.lastTranscript = "";
    }
  }

  public getListening() {
    return this.isListening;
  }
}
