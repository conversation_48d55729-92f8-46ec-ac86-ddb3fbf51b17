export interface IPositionSummary {
  ID: number;
  Title: string;
  MainJobText: string;
  SalaryFrom: string;
  SalaryTo: string;
  Image: string;
  items: IItem[];
}

export interface IPositionRecommendation {
  Theme: string;
  Title: string;
  Description?: string;
}

export interface IPositionSearchResult {
  SearchKey: string;
  TotalPositionCount: number;
  Positions: IPositionSummary[];
  Recommendations: IPositionRecommendation[];
}

// 会話履歴
export interface IItem {
  role: string;
  itemId: string;
  message: string;
  positionSearchResult?: IPositionSearchResult;
}
