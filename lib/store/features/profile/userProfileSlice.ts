import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ProfileState {
  basicInfo: boolean;
  education: boolean;
  experience: boolean;
  preferences: boolean;
}

const initialState: ProfileState = {
  basicInfo: false,
  education: false,
  experience: false,
  preferences: false,
};

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    updateProfileStatus: (state, action: PayloadAction<{ section: keyof ProfileState; completed: boolean }>) => {
      state[action.payload.section] = action.payload.completed;
    },
    loadProfileStatus: (state, action: PayloadAction<ProfileState>) => {
      return { ...state, ...action.payload };
    },
  },
});

export const { updateProfileStatus, loadProfileStatus } = profileSlice.actions;
export default profileSlice.reducer;