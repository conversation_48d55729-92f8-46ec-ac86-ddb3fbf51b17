import { createSlice, PayloadAction } from "@reduxjs/toolkit";

class BasicInfo {
  // お名前（姓）
  lastName = "";
  // お名前（名）
  firstName = "";
  // オナマエ（セイ）
  lastNameKana = "";
  // オナマエ（メイ）
  firstNameKana = "";
  // メールアドレス
  email = "";
  // 電話番号/携帯（市外局番）
  phone = "";
  // 性別
  gender = "";
  // パスワード
  password = "";
  // 生年月
  birthYear = "";
  // 生年日
  birthMonth = "";
  // 都道府県（居住地）
  prefecture = "";
  // 市区町村（居住地）
  city = "";
}

type Education = {
  // 最終学歴 学校区分
  schoolType: string;
  // 学校名
  schoolName: string;
  // 学部・学科系統
  department: string;
  // 専門学校区分
  professionalTrainingCollegeCategory: string;
  // 卒業年
  graduationYear: string;
  // 英語スキル
  englishLevel: string;
};

class Carrer {
  // 経験社数
  expCompanyNum = "";
  // マネジメント経験
  managementPeopleNum = "";
  // 直近企業の勤務先企業名 CompanyName = "";
  // 直近企業の経験業種
  industrySmallID = "";
  // 直近企業の従業員数
  employeeNum = "";
  // 直近企業の雇用形態
  employmentType = "";
  // 直近企業の経験職種
  jobTypeSmallID = "";
  // 直近企業の年収
  income = "";
  // 直近企業の入社年月
  joinYm = "";
  // 直近企業の退職年月
  retireYm = "";
}

class Will {
  // 希望年収
  WillIncome = "";
  // 希望勤務地.海外フラグ
  OverseasFlg = "";
  // 希望勤務地.都道府県
  Prefectures = "";
  // 希望勤務地.市区町村
  Cities = "";
  // 転職希望時期
  WillJobChangePeriod = "";
  // 希望職種.職種大
  WillJobTypesLarges = "";
  // 希望職種.職種中
  WillJobTypesMiddles = "";
  // 希望職種.職種小
  WillJobTypesSmalls = "";
  // TEL同意
  IsRpoAgreement = "";
}

interface ProfileState {
  basicInfo: BasicInfo;
  education: Education;
  carrer: Carrer;
  will: Will;
}

const initialState: ProfileState = {
  basicInfo: new BasicInfo(),
  education: {
    schoolType: "",
    schoolName: "",
    department: "",
    professionalTrainingCollegeCategory: "",
    graduationYear: "",
    englishLevel: "",
  },
  carrer: new Carrer(),
  will: new Will(),
};

const profileSlice = createSlice({
  name: "profile",
  initialState,
  reducers: {
    updateBasicInfo: (state, action: PayloadAction<BasicInfo>) => {
      state.basicInfo = action.payload;
    },
    updateEducation: (state, action: PayloadAction<Education>) => {
      state.education = action.payload;
    },
    updateCarrer: (state, action: PayloadAction<Carrer>) => {
      state.carrer = action.payload;
    },
    updateWill: (state, action: PayloadAction<Will>) => {
      state.will = action.payload;
    },
  },
});

export const { updateBasicInfo, updateEducation, updateCarrer, updateWill } =
  profileSlice.actions;
export default profileSlice.reducer;
