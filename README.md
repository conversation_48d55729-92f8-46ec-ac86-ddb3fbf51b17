# 概要

キャリアエージェントのフロントエンド

現在以下の機能を提供しています。

- キャリアアドバイザーとの会話
- ポジション詳細画面
  - ポジション詳細についてのお問い合わせ
- 登録・応募（作成中）

# ローカルでの起動

## 事前準備

### 環境変数

`.env.example`を`.env.local`にコピーしてください。

## 起動コマンド

`start_frontend.sh`

## 利用方法

ブラウザを起動して、`http://localhost`にアクセスしてください。

# 開発者向け

## 開発言語と主に利用しているライブラリ

- Node.js 22
- Typescript
- Next.js
- Redux
- MUI

## プロジェクト構造

基本Next.jsの[おすすめ](https://nextjs.org/docs/app/getting-started/project-structure)通りに構成しています。

### 画面

- メインチャット
  - app/chat
- ポジション詳細
  - app/positions
  - 基本本体側のポジション画面からそのまま持ってきています。

### コンポネント

- チャット（メインチャット、ポジション詳細画面に利用されています）
  - メインコンポネント
    - components/Chat.tsx
  - チャットアイテム
    - メッセージ
      - components/ChatMessage.tsx
    - ポジション検索結果
      - components/ChatPositionItem.tsx
    - ポジション検索結果のおすすめ
      - components/positions/recommendations
- ポジション詳細
  - 基本上記以外は全部本体側から持ってきているポジション詳細用コンポネント

### Redux

`lib/store`

- lib/store/features/global_state/globalStateSlice.ts
  - グローバル共有されるステート
- lib/store/features/websocket/websocketSlice.ts
  - チャットステート
    - 会話履歴
    - ポジション検索結果（おすすめも含む）
    - セッションIDなど

### その他

`utils/fetch.ts`以外は、基本ポジション詳細のため本体側から持ってきているものです。

## デバッグ

### コンテナで起動する場合

コンテナで起動されるサービスをデバッグする方法なので、ローカルでのNode.jsインストールは不要です。

VSCodeで`launch.json`の`[Frontend]Remote Debug`を実行すればコンテナでフロントエンドを起動し、デバッグできます。

### VSCodeで起動する場合

#### 準備

Node.jsインストール参照

#### 起動方法

VSCodeでMCPサーバーを起動して、デバッグする方法です。

VSCodeで`launch.json`の`[Frontend]Local Debug`を実行すればフロントエンドを起動し、デバッグできます。

## ポジション詳細モックデータについて

ポジション詳細画面開発時に一時的に利用したものなので、いまは基本利用しないです。

### 環境変数設定

`NEXT_PUBLIC_MOCK_API=true`追加

### モックデータ場所

`public/mock/api/responses`

position、companies、businessesにそれぞれjsonファイルを用意する必要があり、
ファイル名はposition IDとなります。

### アクセス方法

http://localhost:3000/positions?positionId=ポジションID

## Node.jsのバージョン管理

### インストール

[nvm](https://github.com/nvm-sh/nvm)を使ってNode.jsのバージョンを管理できます。

ほかにも、`nodenv`や`anyenv`などのNode.jsバージョン管理ツールもありますので、自由に選んでください。

#### nvm導入

https://github.com/nvm-sh/nvm?tab=readme-ov-file#installing-and-updating

https://github.com/nvm-sh/nvm?tab=readme-ov-file#usage

#### nodenv導入

https://qiita.com/282Haniwa/items/a764cf7ef03939e4cbb1

#### anyenv導入

https://qiita.com/rinpa/items/81766cd6a7b23dea9f3c

## 静的ファイル作成

### ローカルで作成

#### 開発環境向け

- `build`フォルダを削除
- `.env.example`を`.env.dev`にコピーして、開発環境のエンドポイントに変更
- `npm run build`を実行

#### ステージング環境向け

- `build`フォルダを削除
- `.env.example`を`.env.stg`にコピーして、ステージング環境のエンドポイントに変更
- `npm run build`を実行

#### 本番環境向け

- `build`フォルダを削除
- `.env.example`を`.env.prd`にコピーして、本番環境のエンドポイントに変更
- `npm run build`を実行

### Githubアクションより作成

#### 開発環境向け

`develop`ブランチにマージすれば、Github Actionが検証環境向けの静的ページを生成し、ブランチ`build-dev`にビルドされた静的ファイルが保存されます。

#### ステージング環境向け

まだない。

#### 本番環境向け

`main`ブランチにマージすれば、Github Actionが検証環境向けの静的ページを生成し、ブランチ`build-prod`にビルドされた静的ファイルが保存されます。
