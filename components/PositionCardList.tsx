import * as React from "react";
import Card from "@mui/material/Card";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import { IPositionSummary } from "@/lib/common";
import { Box } from "@mui/material";
import { useRouter } from "next/navigation";

import "./PositionCardList.scss";

interface IPositionCardListProps {
  positions: IPositionSummary[];
}

export default function PositionCard({ positions }: IPositionCardListProps) {
  const router = useRouter();
  return (
    <Box className="position-cards-list">
      {positions.map((position) => (
        <Card key={position.ID} sx={{ maxWidth: 600 }}>
          <CardMedia
            component="img"
            alt={position.Title}
            width={600}
            image={
              position.Image
                ? position.Image
                : `https://picsum.photos/seed/${position.ID}/200/140`
            }
            // TODO: Change to a default picture
            // TODO: https://github.com/MIIDAS-Company/miidas_aica_frontend/pull/6#discussion_r2156229113
            onError={(e) => {
              (e.currentTarget as HTMLImageElement).src =
                `https://picsum.photos/seed/${position.ID}/200/140`;
            }}
          />
          <CardContent>
            <Typography gutterBottom variant="h5" component="div">
              {position.Title}
            </Typography>
            <Typography component="p" className="position-description">
              {position.MainJobText}
            </Typography>
          </CardContent>
          <CardActions sx={{ justifyContent: "flex-end" }}>
            <Button
              size="medium"
              onClick={() =>
                router.push(`/positions/?positionId=${position.ID}`)
              }
            >
              詳細を見る
            </Button>
          </CardActions>
        </Card>
      ))}
    </Box>
  );
}
