"use client";

import {
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  <PERSON>,
  CardActions,
  <PERSON><PERSON>,
  Chip,
} from "@mui/material";
import WarningIcon from "@mui/icons-material/Warning";
import CheckIcon from "@mui/icons-material/Check";
import { useRouter } from "next/navigation";
import { useAppSelector } from "@/lib/store/hooks";
import { useCallback } from "react";

export default function Profile() {
  const router = useRouter();

  const basicInfo = useAppSelector((state) => state.profile.basicInfo);
  const education = useAppSelector((state) => state.profile.education);
  const carrer = useAppSelector((state) => state.profile.carrer);
  const will = useAppSelector((state) => state.profile.will);

  const basicInfoCompleted = useCallback(() => {
    const values = Object.values(basicInfo);
    return values.every(
      (value) => value !== "" && value != null && value !== undefined
    );
  }, [basicInfo]);

  const educationCompleted = useCallback(() => {
    const values = Object.values(education);
    return values.every(
      (value) => value !== "" && value != null && value !== undefined
    );
  }, [education]);

  const carrerCompleted = useCallback(() => {
    const values = Object.values(carrer);
    return values.every(
      (value) => value !== "" && value != null && value !== undefined
    );
  }, [carrer]);

  const willCompleted = useCallback(() => {
    const values = Object.values(will);
    return values.every(
      (value) => value !== "" && value != null && value !== undefined
    );
  }, [will]);

  const profiles = [
    {
      title: "基本情報",
      description: "氏名、メールアドレス、パスワードなど",
      completed: basicInfoCompleted(),
      detailPath: "/basic-info"
    },
    {
      title: "学歴",
      description: "最終学歴、卒業年など",
      completed: educationCompleted(),
      detailPath: "/education"
    },
    {
      title: "経歴",
      description: "経験した企業、経験した仕事内容など",
      completed: carrerCompleted(),
      detailPath: "/carrer"
    },
    {
      title: "希望条件",
      description: "希望する年収、勤務地、仕事内容など",
      completed: willCompleted(),
      detailPath: "/will"
    },
  ];

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
      {profiles.map((profile, index) => (
        <Card key={index} sx={{ position: "relative" }}>
          {profile.completed && (
            <Chip
              icon={<CheckIcon color="success" />}
              label="入力済み"
              size="small"
              variant="outlined"
              sx={{
                position: "absolute",
                top: 8,
                right: 8,
                zIndex: 1,
                color: "success.main",
                borderColor: "success.main",
                px: 0.5,
                py: 0.5,
                "& .MuiChip-label": {
                  color: "success.main",
                  px: 1.5,
                },
              }}
            />
          )}
          {!profile.completed && (
            <Chip
              icon={<WarningIcon color="error" />}
              label="未入力"
              size="small"
              variant="outlined"
              sx={{
                position: "absolute",
                top: 8,
                right: 8,
                zIndex: 1,
                color: "error.main",
                borderColor: "error.main",
                px: 0.5,
                py: 0.5,
                "& .MuiChip-label": {
                  color: "error.main",
                  px: 1.5,
                },
              }}
            />
          )}
          <CardContent>
            <Typography variant="h6" component="h2" gutterBottom>
              {profile.title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {profile.description}
            </Typography>
          </CardContent>
          <CardActions sx={{ justifyContent: "flex-end" }}>
            <Button size="medium" onClick={() => router.push(profile.detailPath)}>
              詳細を見る
            </Button>
          </CardActions>
        </Card>
      ))}
    </Box>
  );
}
