"use client";

import {
  Card,
  CardContent,
  Typography,
  Box,
  CardActions,
  But<PERSON>,
  Chip,
} from "@mui/material";
import WarningIcon from "@mui/icons-material/Warning";
import CheckIcon from "@mui/icons-material/Check";
import { useRouter } from "next/navigation";

export default function UserProfile() {
  const router = useRouter();
  const profiles = [
    {
      title: "基本情報",
      description: "氏名、メールアドレス、パスワードなど",
      allInputted: false,
    },
    {
      title: "学歴",
      description: "最終学歴、卒業年など",
      allInputted: false,
    },
    {
      title: "経歴",
      description: "経験した企業、経験した仕事内容など",
      allInputted: false,
    },
    {
      title: "希望条件",
      description: "希望する年収、勤務地、仕事内容など",
      allInputted: true,
    },
  ];

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
      {profiles.map((profile, index) => (
        <Card key={index} sx={{ position: "relative" }}>
          {profile.allInputted && (
            <Chip
              icon={<CheckIcon color="success" />}
              label="入力済み"
              size="small"
              variant="outlined"
              sx={{
                position: "absolute",
                top: 8,
                right: 8,
                zIndex: 1,
                color: "success.main",
                borderColor: "success.main",
                px: 0.5,
                py: 0.5,
                "& .MuiChip-label": {
                  color: "success.main",
                  px: 1.5,
                },
              }}
            />
          )}
          {!profile.allInputted && (
            <Chip
              icon={<WarningIcon color="error" />}
              label="未入力"
              size="small"
              variant="outlined"
              sx={{
                position: "absolute",
                top: 8,
                right: 8,
                zIndex: 1,
                color: "error.main",
                borderColor: "error.main",
                px: 0.5,
                py: 0.5,
                "& .MuiChip-label": {
                  color: "error.main",
                  px: 1.5,
                },
              }}
            />
          )}
          <CardContent>
            <Typography variant="h6" component="h2" gutterBottom>
              {profile.title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {profile.description}
            </Typography>
          </CardContent>
          <CardActions sx={{ justifyContent: "flex-end" }}>
            <Button size="medium" onClick={() => router.push("/profile")}>
              詳細を見る
            </Button>
          </CardActions>
        </Card>
      ))}
    </Box>
  );
}
