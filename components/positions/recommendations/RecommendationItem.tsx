"use client";

import React, { useEffect, useState } from "react";
import {
  Box,
  Link,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Button,
  Collapse,
  Typography,
} from "@mui/material";
import { IPositionRecommendation, IPositionSummary } from "@/lib/common";
import { useAppDispatch } from "@/lib/store/hooks";
import { updatePositions } from "@/lib/store/features/websocket/websocketSlice";
import { useRouter } from "next/navigation";
import getRecommendationPositions from "./getRecommendation";

interface IRecommendationItemProps {
  searchKey: string;
  recommendation: IPositionRecommendation;
}

export default function RecommendationItem({
  searchKey,
  recommendation,
}: IRecommendationItemProps) {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(true);
  const [positions, setPositions] = useState<IPositionSummary[]>([]);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    getRecommendationPositions(searchKey, recommendation.Theme).then(
      (positionResult) => {
        if (positionResult.data?.Positions) {
          const fetchedPositions = positionResult.data.Positions;

          setPositions(fetchedPositions);

          dispatch(updatePositions(fetchedPositions));
        }
        setLoading(false);
      },
    );
  }, [searchKey, recommendation.Theme, dispatch]);

  const label = loading
    ? `${recommendation.Title}（検索中…）`
    : `${recommendation.Title}（${positions.length}件）`;

  return (
    <Box sx={{ mb: 2 }} id={recommendation.Theme}>
      {loading || positions.length <= 0 ? (
        <Typography
          sx={{
            display: "block",
            color: "text.disabled",
            cursor: "default",
          }}
        >
          {label}
        </Typography>
      ) : (
        <Link
          component="button"
          underline="none"
          onClick={() => setOpen(!open)}
          sx={{
            display: "block",
            fontWeight: open ? "bold" : "normal",
            color: "success.main",
          }}
        >
          {label}
        </Link>
      )}

      <Collapse in={open} unmountOnExit>
        {recommendation.Description && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            {recommendation.Description}
          </Typography>
        )}
        <Box
          sx={{
            display: "flex",
            gap: 2,
            overflowX: "auto",
            py: 1,
          }}
        >
          {positions.map((item) => (
            <Card
              key={recommendation.Theme + "/" + item.ID}
              sx={{ maxWidth: 200, flexShrink: 0 }}
            >
              <CardMedia
                component="img"
                width="120"
                // TODO: Change to a default picture
                // TODO: https://github.com/MIIDAS-Company/miidas_aica_frontend/pull/6#discussion_r2156229113
                image={
                  item.Image ? item.Image : "https://picsum.photos/200/140"
                }
                onError={(e) => {
                  console.error(e);
                  (e.currentTarget as HTMLImageElement).src =
                    "https://picsum.photos/200/140";
                }}
                alt={item.Title}
                sx={{ objectFit: "contain", height: 120 }}
              />
              <CardContent sx={{ p: 1 }}>
                <Typography variant="subtitle2" noWrap>
                  {item.Title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {item.SalaryFrom ? `${item.SalaryFrom}万円～` : ""}
                  {item.SalaryTo}万円
                </Typography>
              </CardContent>
              <CardActions sx={{ justifyContent: "flex-end", p: 1 }}>
                <Button
                  size="small"
                  onClick={() =>
                    router.push(`/positions/?positionId=${item.ID}`)
                  }
                >
                  詳細を見る
                </Button>
              </CardActions>
            </Card>
          ))}
        </Box>
      </Collapse>
    </Box>
  );
}
