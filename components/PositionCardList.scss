.position-cards-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;

  .position-description {
    color: var(--text-secondary);
    height: 140pt;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    line-clamp: 8;
    -webkit-line-clamp: 8;
    -webkit-box-orient: vertical;
    font-size: 1rem;
    line-height: 1.8;
  }

  button {
    color: var(--mui-palette-success-main);
    font-size: 1rem;
  }
}
