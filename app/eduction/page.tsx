"use client";

import { useRouter } from "next/navigation";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import TextField from "@mui/material/TextField";
import Grid from "@mui/material/Grid2";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import LockIcon from "@mui/icons-material/Lock";
import CloseIcon from "@mui/icons-material/Close";
import IconButton from "@mui/material/IconButton";
import { FormControlLabel, Radio, RadioGroup } from "@mui/material";

type ProfileFormData = {
  lastName: string;
  firstName: string;
  lastNameKana: string;
  firstNameKana: string;
  email: string;
  phone: string;
  gender: string;
  password: string;
  birthYear: string;
  birthMonth: string;
  address: string;
};

export default function EducationPage() {
  const router = useRouter();

  // React Hook Form の設定
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<ProfileFormData>({
    defaultValues: {
      lastName: "",
      firstName: "",
      lastName<PERSON>ana: "",
      firstNameKana: "",
      email: "",
      phone: "",
      gender: "",
      password: "",
      birthYear: "",
      birthMonth: "",
      address: "",
    },
    mode: "onBlur", // フォーカスが外れたときに検証
  });

  // フォーム送信時の処理
  const onSubmit = (data: ProfileFormData) => {
    console.debug("保存されたデータ:", data);
    // データを保存した後、チャットページに戻る
    router.push("/");
  };

  const close = () => {
    router.back();
  };

  return (
    <Box
      sx={{
        height: "100vh",
      }}
    >
      <Box
        sx={{
          maxWidth: 600,
          height: "100vh",
          margin: "0 auto",
          backgroundColor: "white",
          borderRadius: 2,
          boxShadow: "0 1px 3px rgba(0,0,0,0.12)",
          overflow: "auto",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Box
          sx={{
            display: "flex",
            backgroundColor: "#e3f2fd",
            borderBottom: "1px solid #e0e0e0",
            padding: 2,
            position: "sticky",
            top: 0,
            zIndex: 100,
          }}
        >
          <Typography
            variant="h6"
            sx={{
              width: "100%",
              textAlign: "center",
            }}
          >
            基本情報
          </Typography>
          <IconButton
            onClick={close}
            sx={{
              position: "absolute",
              top: 8,
              right: 8,
              zIndex: 10,
            }}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </Box>

        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          sx={{ p: 3, flex: 1, display: "flex", flexDirection: "column" }}
        >
          <Box sx={{ flex: 1 }}>
            <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
              <LockIcon fontSize="small" color="success" sx={{ mr: 1 }} />
              <Typography variant="body2" color="success">
                は自分が応募した企業にのみ表示されます
              </Typography>
            </Box>

            <Grid container spacing={2}>
              <Grid size={6}>
                <Typography variant="body2" gutterBottom>
                  姓
                  <LockIcon
                    fontSize="small"
                    color="success"
                    sx={{ ml: 0.5, verticalAlign: "middle", fontSize: 16 }}
                  />
                </Typography>
                <Controller
                  name="lastName"
                  control={control}
                  rules={{
                    required: "空白になっています",
                    pattern: {
                      value: /^[ぁ-んァ-ヶ一-龯]$/,
                      message: "漢字、ひらがな、カタカナのみ入力可能です",
                    },
                    maxLength: {
                      value: 50,
                      message: "50文字まで入力してください",
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      placeholder="姓を入力"
                      variant="outlined"
                      size="small"
                      error={!!errors.lastName}
                      helperText={errors.lastName?.message}
                      slotProps={{
                        formHelperText: { sx: { color: "error.main" } },
                      }}
                    />
                  )}
                />
              </Grid>
              <Grid size={6}>
                <Typography variant="body2" gutterBottom>
                  名
                  <LockIcon
                    fontSize="small"
                    color="success"
                    sx={{ ml: 0.5, verticalAlign: "middle", fontSize: 16 }}
                  />
                </Typography>
                <Controller
                  name="firstName"
                  control={control}
                  rules={{
                    required: "空白になっています",
                    pattern: {
                      value: /^[ぁ-んァ-ヶ一-龯]+$/,
                      message: "漢字、ひらがな、カタカナのみ入力可能です",
                    },
                    maxLength: {
                      value: 50,
                      message: "50文字まで入力してください",
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      placeholder="名を入力"
                      variant="outlined"
                      size="small"
                      error={!!errors.firstName}
                      helperText={errors.firstName?.message}
                      slotProps={{
                        formHelperText: { sx: { color: "error.main" } },
                      }}
                    />
                  )}
                />
              </Grid>
              <Grid size={6}>
                <Typography variant="body2" gutterBottom>
                  姓（カナ）
                  <LockIcon
                    fontSize="small"
                    color="success"
                    sx={{ ml: 0.5, verticalAlign: "middle", fontSize: 16 }}
                  />
                </Typography>
                <Controller
                  name="lastNameKana"
                  control={control}
                  rules={{
                    required: "空白になっています",
                    pattern: {
                      value: /^[ァ-ヶー]+$/,
                      message: "カタカナのみ入力可能です",
                    },
                    maxLength: {
                      value: 50,
                      message: "50文字まで入力してください",
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      placeholder="姓を入力"
                      variant="outlined"
                      size="small"
                      error={!!errors.lastNameKana}
                      helperText={errors.lastNameKana?.message}
                      slotProps={{
                        formHelperText: { sx: { color: "error.main" } },
                      }}
                    />
                  )}
                />
              </Grid>
              <Grid size={6}>
                <Typography variant="body2" gutterBottom>
                  名（カナ）
                  <LockIcon
                    fontSize="small"
                    color="success"
                    sx={{ ml: 0.5, verticalAlign: "middle", fontSize: 16 }}
                  />
                </Typography>
                <Controller
                  name="firstNameKana"
                  control={control}
                  rules={{
                    required: "空白になっています",
                    pattern: {
                      value: /^[ァ-ヶー]+$/,
                      message: "カタカナのみ入力可能です",
                    },
                    maxLength: {
                      value: 50,
                      message: "50文字まで入力してください",
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      placeholder="名を入力"
                      variant="outlined"
                      size="small"
                      error={!!errors.firstNameKana}
                      helperText={errors.firstNameKana?.message}
                      slotProps={{
                        formHelperText: { sx: { color: "error.main" } },
                      }}
                    />
                  )}
                />
              </Grid>
              <Grid size={12}>
                <Typography variant="body2" gutterBottom>
                  メールアドレス
                  <LockIcon
                    fontSize="small"
                    color="success"
                    sx={{ ml: 0.5, verticalAlign: "middle", fontSize: 16 }}
                  />
                </Typography>
                <Controller
                  name="email"
                  control={control}
                  rules={{
                    required: "空白になっています",
                    pattern: {
                      value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                      message: "有効なメールアドレスを入力してください",
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      placeholder="メールアドレスを入力"
                      variant="outlined"
                      size="small"
                      error={!!errors.email}
                      helperText={errors.email?.message}
                      slotProps={{
                        formHelperText: { sx: { color: "error.main" } },
                      }}
                    />
                  )}
                />
              </Grid>
              <Grid size={12}>
                <Typography variant="body2" gutterBottom>
                  電話番号
                  <LockIcon
                    fontSize="small"
                    color="success"
                    sx={{ ml: 0.5, verticalAlign: "middle", fontSize: 16 }}
                  />
                </Typography>
                <Controller
                  name="phone"
                  control={control}
                  rules={{
                    required: "空白になっています",
                    pattern: {
                      value: /^[0-9]{10,11}$/,
                      message:
                        "有効な電話番号を入力してください（ハイフンなし）",
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      placeholder="電話番号を入力"
                      variant="outlined"
                      size="small"
                      error={!!errors.phone}
                      helperText={errors.phone?.message}
                      slotProps={{
                        formHelperText: { sx: { color: "error.main" } },
                      }}
                    />
                  )}
                />
                <Typography variant="caption" color="text.secondary">
                  ※ハイフンなし
                </Typography>
              </Grid>
              <Grid size={12}>
                <Typography variant="body2" gutterBottom>
                  性別
                </Typography>
                <Controller
                  name="gender"
                  control={control}
                  rules={{
                    required: "性別を選択してください。",
                  }}
                  render={({ field }) => (
                    <Box>
                      <RadioGroup
                        {...field}
                        row
                        sx={{
                          width: "100%",
                          justifyContent: "space-between",
                        }}
                      >
                        <FormControlLabel
                          value="2"
                          control={<Radio />}
                          label="女性"
                          sx={{
                            flex: 1,
                            margin: 0,
                          }}
                        />
                        <FormControlLabel
                          value="1"
                          control={<Radio />}
                          label="男性"
                          sx={{
                            flex: 1,
                            margin: 0,
                          }}
                        />
                      </RadioGroup>
                      {errors.gender && (
                        <Typography
                          variant="caption"
                          sx={{
                            color: "error.main",
                            mt: 0.5,
                            ml: 1.75,
                            display: "block",
                          }}
                        >
                          {errors.gender.message}
                        </Typography>
                      )}
                    </Box>
                  )}
                />
              </Grid>
              <Grid size={12}>
                <Typography variant="body2" gutterBottom>
                  パスワード
                </Typography>
                <Controller
                  name="password"
                  control={control}
                  rules={{
                    required: "空白になっています",
                    pattern: {
                      value: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$/,
                      message:
                        "半角英数字を組み合わせ、8文字以上、16文字以内で入力してください",
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      type="password"
                      placeholder="パスワードを入力"
                      variant="outlined"
                      size="small"
                      error={!!errors.password}
                      helperText={errors.password?.message}
                      slotProps={{
                        formHelperText: { sx: { color: "error.main" } },
                        input: {
                          inputMode: "text",
                          autoComplete: "new-password",
                        },
                      }}
                    />
                  )}
                />
                <Typography variant="caption" color="text.secondary">
                  ※アルファベットと数字の組み合わせ8文字以上
                </Typography>
              </Grid>
              <Grid size={6}>
                <Typography variant="body2" gutterBottom>
                  生まれ年
                </Typography>
                <Controller
                  name="birthYear"
                  control={control}
                  rules={{
                    required: "空白になっています",
                    pattern: {
                      value: /^[0-9]+$/,
                      message: "半角数字のみ入力可能です",
                    },
                    validate: (value) => {
                      const num = parseInt(value);
                      return (
                        (num >= 15 && num <= 90) ||
                        "15〜90の範囲で入力してください"
                      );
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      placeholder="○○"
                      variant="outlined"
                      size="small"
                      error={!!errors.birthYear}
                      helperText={errors.birthYear?.message}
                      slotProps={{
                        formHelperText: { sx: { color: "error.main" } },
                        input: {
                          endAdornment: (
                            <Typography variant="body2" color="textDisabled">
                              歳
                            </Typography>
                          ),
                          inputMode: "numeric",
                        },
                      }}
                    />
                  )}
                />
              </Grid>
              <Grid size={6}>
                <Typography variant="body2" gutterBottom>
                  生まれ月
                </Typography>
                <Controller
                  name="birthMonth"
                  control={control}
                  rules={{
                    required: "空白になっています",
                    pattern: {
                      value: /^[0-9]+$/,
                      message: "半角数字のみ入力可能です",
                    },
                    validate: (value) => {
                      const num = parseInt(value);
                      return (
                        (num >= 1 && num <= 12) ||
                        "1〜12の数字のみ入力してください"
                      );
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      placeholder="○○"
                      variant="outlined"
                      size="small"
                      error={!!errors.birthMonth}
                      helperText={errors.birthMonth?.message}
                      slotProps={{
                        formHelperText: { sx: { color: "error.main" } },
                        input: {
                          endAdornment: (
                            <Typography variant="body2" color="textDisabled">
                              月
                            </Typography>
                          ),
                          inputMode: "numeric",
                        },
                      }}
                    />
                  )}
                />
              </Grid>
              <Grid size={12}>
                <Typography variant="body2" gutterBottom>
                  お住まいの市区町村名
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  例: 東京都新宿区、大阪府大阪市、北海道札幌市
                </Typography>
                <Controller
                  name="address"
                  control={control}
                  rules={{
                    required: "空白になっています",
                    pattern: {
                      value:
                        /^([ぁ-んァ-ヶ一-龯]+)(都|道|府|県)([ぁ-んァ-ヶ一-龯]+)(市|区|町|村)$/,
                      message: "都道府県と市区町村の形式で入力してください",
                    },
                    maxLength: {
                      value: 15,
                      message: "15文字以内で入力してください",
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      placeholder="◯◯県◯◯市"
                      variant="outlined"
                      size="small"
                      error={!!errors.address}
                      helperText={errors.address?.message}
                      slotProps={{
                        formHelperText: { sx: { color: "error.main" } },
                      }}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Box>

          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              mt: 4,
              borderTop: "1px solid #e0e0e0",
              pt: 2,
              position: "sticky",
              bottom: 0,
              backgroundColor: "white",
            }}
          >
            <Button
              onClick={close}
              variant="outlined"
              sx={{ width: "45%", borderRadius: 100 }}
            >
              キャンセル
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="success"
              sx={{
                width: "45%",
                borderRadius: 100,
                backgroundColor: "#4C576C",
              }}
            >
              保存する
            </Button>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}
