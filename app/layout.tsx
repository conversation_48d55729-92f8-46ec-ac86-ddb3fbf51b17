"use client";

import "./globals.css";
import Head from "next/head";
import localFont from "next/font/local";
import ThemeOptions from "../lib/themeOptions";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import SafeArea from "@/components/SafeArea";
import StoreProvider from "./StoreProvider";
import WebSocketProvider from "@/app/WebSocketProvider";

const theme = createTheme(ThemeOptions);

const notoSansJP = localFont({
  src: "./assets/fonts/NotoSansJP-VariableFont_wght.ttf",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <Head>
        <title>AIキャリアアドバイザー ミイダス</title>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, minimum-scale=1"
        ></meta>
      </Head>
      <body className={notoSansJP.className}>
        <StoreProvider>
          <WebSocketProvider>
            <ThemeProvider theme={theme}>
              <SafeArea>{children}</SafeArea>
            </ThemeProvider>
          </WebSocketProvider>
        </StoreProvider>
      </body>
    </html>
  );
}
