"use client";

import "./page.scss";
import VersionToast from "@/components/VersionToast";
import SurveyLink from "@/components/SurveyLink";
import { useAppDispatch, useAppSelector } from "@/lib/store/hooks";
import Chat from "@/components/Chat";
import Box from "@mui/material/Box";
import { PAGE_NAME } from "@/constants/page";
import { closeVersionToast } from "@/lib/store/features/global_state/globalStateSlice";

const VERSION_NUMBER: string = "2025/6";
const VERSION_FEATURES: Array<string> = [
  "3大条件（職種、勤務地、年収）の取得とポジション検索",
  "居住地から通える範囲のポジション検索",
  "曖昧な職種または曖昧な業種でもポジション検索可能",
  "ポジション詳細への遷移",
  "ポジション詳細の専用チャット",
  "ポジションの提案（例：高年収、残業が少ないなど）",
];

export default function ChatPage() {
  const dispatch = useAppDispatch();
  const versionToastClosed = useAppSelector(
    (state) => state.globalState.versionToastClosed,
  );
  const sessionID = useAppSelector((state) => state.websocket.sessionID);

  if (!versionToastClosed) {
    return (
      <VersionToast
        onClose={() => dispatch(closeVersionToast())}
        versionName={VERSION_NUMBER}
        features={VERSION_FEATURES}
      />
    );
  }

  return (
    <>
      {sessionID?.length > 0 && <SurveyLink sessionId={sessionID} />}
      <Box className="main-container">
        <Chat currentPage={PAGE_NAME.CHAT} />
      </Box>
    </>
  );
}
