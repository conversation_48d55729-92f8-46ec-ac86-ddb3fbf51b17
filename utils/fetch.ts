import { store } from "@/lib/store";

export async function readMockData(
  path: string,
  id: string,
  errorMsg: string
): Promise<{
  data: any;
  httpStatus: number | null;
  error: Error | null;
}> {
  try {
    const response = await fetch(`/mock/api/responses/${path}/${id}.json`);
    if (response.ok) {
      return {
        data: await response.json(),
        httpStatus: response.status,
        error: null,
      };
    } else {
      return {
        data: null,
        httpStatus: response.status,
        error: new Error(`Failed to fetch mock ${path} data`),
      };
    }
  } catch (err) {
    console.error(`Error loading mock ${path} data:`, err);
    return {
      data: null,
      httpStatus: null,
      error: new Error(errorMsg, { cause: err }),
    };
  }
}

export async function fetchApiData(
  path: string,
  errorMsg: string,
  data?: any
): Promise<{
  data: any;
  httpStatus: number | null;
  error: Error | null;
}> {
  const sessionID = store.getState().websocket.sessionID;
  const apiEndpoint = process.env.NEXT_PUBLIC_API_ENDPOINT;
  const url = `${apiEndpoint}/${path}`;
  try {
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };
    if (sessionID) {
      headers["X-SESSION-ID"] = sessionID;

      const now = new Date();
      const pad = (n: number, len = 2) => n.toString().padStart(len, "0");
      const yyyy = now.getFullYear();
      const MM = pad(now.getMonth() + 1);
      const dd = pad(now.getDate());
      const HH = pad(now.getHours());
      const mm = pad(now.getMinutes());
      const SS = pad(now.getSeconds());
      const ssssss = pad(now.getMilliseconds(), 6);
      const random = Math.floor(Math.random() * 1000000);

      headers["X-REQUEST-ID"] =
        `${yyyy}${MM}${dd}${HH}${mm}${SS}.${ssssss}.${random}`;
    }
    const fetchOptions: RequestInit = data
      ? {
          method: "POST",
          headers,
          body: JSON.stringify(data),
        }
      : {
          headers,
        };
    const response = await fetch(url, fetchOptions);
    if (response.ok) {
      const result = await response.json();
      return {
        data: result,
        httpStatus: response.status,
        error: null,
      };
    } else {
      return {
        data: null,
        httpStatus: response.status,
        error: new Error(errorMsg),
      };
    }
  } catch (err) {
    console.error(err);
    return {
      data: null,
      httpStatus: null,
      error: new Error(errorMsg, { cause: err }),
    };
  }
}
